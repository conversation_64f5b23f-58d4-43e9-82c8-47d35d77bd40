import React from "react";

const ImageShowcaseSection = () => {
  return (
    <section className="w-full pt-0 pb-8 sm:pb-12 bg-white" id="showcase">
      <div className="container px-4 sm:px-6 lg:px-8 mx-auto">
        <div className="max-w-3xl mx-auto text-center mb-8 sm:mb-12 animate-on-scroll">
          <h2 className="text-3xl sm:text-4xl font-display font-bold tracking-tight text-gray-900 mb-3 sm:mb-4">
            Experience the WorldWide Presence
          </h2>
          <p className="text-base sm:text-lg text-black ">
          Delivering Power Solutions Beyond Borders
          Empowering industries across continents with reliable energy, protection, and efficiency.
          </p>
        </div>
        
        <div className="mx-auto max-w-4xl animate-on-scroll">
          <div className="w-full mb-6">
            <img 
              src="/map.png" 
              alt="World map showing global presence and reach" 
              className="w-full h-auto object-cover"
            />
          </div>
          <div className="text-center">
            <p className="text-black text-sm sm:text-base">
            Atandra Energy, under the KRYKARD brand, has extended its reach beyond India to serve a growing global clientele. With a strategic presence in key regions across Asia, the Middle East, and Africa, we continue to deliver trusted energy management and power conditioning solutions to industries worldwide. Our international footprint reflects our commitment to innovation, quality, and customer satisfaction, regardless of geography.
            </p>
          </div>
        </div>
      </div>
    </section>
  );
};

export default ImageShowcaseSection;
