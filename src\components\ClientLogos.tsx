import React, { useState, useEffect } from "react";

interface ClientLogo {
  id: string;
  name: string;
  logo: string;
  website?: string;
}

const ClientLogos = () => {
  const [clientLogos, setClientLogos] = useState<ClientLogo[]>([]);

  // Default client logos - you can modify this array or load from an API
  const defaultLogos: ClientLogo[] = [
    {
      id: "1",
      name: "Microsoft",
      logo: "https://upload.wikimedia.org/wikipedia/commons/thumb/4/44/Microsoft_logo.svg/512px-Microsoft_logo.svg.png",
      website: "https://microsoft.com"
    },
    {
      id: "2", 
      name: "Google",
      logo: "https://upload.wikimedia.org/wikipedia/commons/thumb/2/2f/Google_2015_logo.svg/512px-Google_2015_logo.svg.png",
      website: "https://google.com"
    },
    {
      id: "3",
      name: "<PERSON>",
      logo: "https://upload.wikimedia.org/wikipedia/commons/thumb/f/fa/Apple_logo_black.svg/512px-Apple_logo_black.svg.png",
      website: "https://apple.com"
    },
    {
      id: "4",
      name: "Amazon",
      logo: "https://upload.wikimedia.org/wikipedia/commons/thumb/a/a9/Amazon_logo.svg/512px-Amazon_logo.svg.png",
      website: "https://amazon.com"
    },
    {
      id: "5",
      name: "Meta",
      logo: "https://upload.wikimedia.org/wikipedia/commons/thumb/7/7b/Meta_Platforms_Inc._logo.svg/512px-Meta_Platforms_Inc._logo.svg.png",
      website: "https://meta.com"
    },
    {
      id: "6",
      name: "Tesla",
      logo: "https://upload.wikimedia.org/wikipedia/commons/thumb/b/bb/Tesla_T_symbol.svg/512px-Tesla_T_symbol.svg.png",
      website: "https://tesla.com"
    }
  ];

  useEffect(() => {
    // You can replace this with an API call to fetch dynamic logos
    setClientLogos(defaultLogos);
  }, []);

  const handleLogoClick = (website?: string) => {
    if (website) {
      window.open(website, '_blank', 'noopener,noreferrer');
    }
  };

  return (
    <section id="client-logos" className="w-full bg-gray-50 py-12 md:py-16">
      <div className="section-container">
        <div className="text-center mb-12">
          <h2 className="section-title text-gray-900 mb-4">
            Trusted by Industry Leaders
          </h2>
          <p className="section-subtitle text-gray-600 mx-auto">
            Join thousands of companies that trust our solutions for their energy management needs
          </p>
        </div>

        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-8 items-center">
          {clientLogos.map((client) => (
            <div
              key={client.id}
              className="group flex items-center justify-center p-4 bg-white rounded-lg shadow-sm hover:shadow-md transition-all duration-300 cursor-pointer"
              onClick={() => handleLogoClick(client.website)}
            >
              <img
                src={client.logo}
                alt={`${client.name} logo`}
                className="max-h-12 w-auto object-contain filter grayscale group-hover:grayscale-0 transition-all duration-300 group-hover:scale-110"
                onError={(e) => {
                  // Fallback if image fails to load
                  const target = e.target as HTMLImageElement;
                  target.src = `https://via.placeholder.com/120x60/f3f4f6/6b7280?text=${client.name}`;
                }}
              />
            </div>
          ))}
        </div>

        {/* Optional: Add a call-to-action */}
        <div className="text-center mt-12">
          <p className="text-gray-600 mb-6">
            Want to see your logo here? Join our growing list of satisfied clients.
          </p>
          <button className="inline-flex items-center px-6 py-3 bg-pulse-600 text-white font-medium rounded-lg hover:bg-pulse-700 transition-colors duration-200">
            Become a Client
          </button>
        </div>
      </div>
    </section>
  );
};

export default ClientLogos;
